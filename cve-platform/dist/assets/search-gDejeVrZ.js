import{d as k,h as o,y as h,L as x,z as M,A as n,M as O,al as V,F as D,r as q,c as N,a6 as W,G as L,R as _,I as K,cc as Z,a3 as G,p as Q,aa as S,a2 as J,ar as X,cd as Y,t as T,as as ee,a4 as $,ce as ae,ak as re,u as te,bn as le,i as oe,E as se,aO as P}from"./index-BNcvR5C7.js";function A(e,i){let{target:t}=e;for(;t;){if(t.dataset&&t.dataset[i]!==void 0)return!0;t=t.parentElement}return!1}const ne=k({name:"ChevronLeft",render(){return o("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M10.3536 3.14645C10.5488 3.34171 10.5488 3.65829 10.3536 3.85355L6.20711 8L10.3536 12.1464C10.5488 12.3417 10.5488 12.6583 10.3536 12.8536C10.1583 13.0488 9.84171 13.0488 9.64645 12.8536L5.14645 8.35355C4.95118 8.15829 4.95118 7.84171 5.14645 7.64645L9.64645 3.14645C9.84171 2.95118 10.1583 2.95118 10.3536 3.14645Z",fill:"currentColor"}))}}),ie=k({name:"ChevronRight",render(){return o("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},o("path",{d:"M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z",fill:"currentColor"}))}}),de=h("collapse","width: 100%;",[h("collapse-item",`
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 margin: var(--n-item-margin);
 `,[x("disabled",[n("header","cursor: not-allowed;",[n("header-main",`
 color: var(--n-title-text-color-disabled);
 `),h("collapse-item-arrow",`
 color: var(--n-arrow-color-disabled);
 `)])]),h("collapse-item","margin-left: 32px;"),M("&:first-child","margin-top: 0;"),M("&:first-child >",[n("header","padding-top: 0;")]),x("left-arrow-placement",[n("header",[h("collapse-item-arrow","margin-right: 4px;")])]),x("right-arrow-placement",[n("header",[h("collapse-item-arrow","margin-left: 4px;")])]),n("content-wrapper",[n("content-inner","padding-top: 16px;"),V({duration:"0.15s"})]),x("active",[n("header",[x("active",[h("collapse-item-arrow","transform: rotate(90deg);")])])]),M("&:not(:first-child)","border-top: 1px solid var(--n-divider-color);"),O("disabled",[x("trigger-area-main",[n("header",[n("header-main","cursor: pointer;"),h("collapse-item-arrow","cursor: default;")])]),x("trigger-area-arrow",[n("header",[h("collapse-item-arrow","cursor: pointer;")])]),x("trigger-area-extra",[n("header",[n("header-extra","cursor: pointer;")])])]),n("header",`
 font-size: var(--n-title-font-size);
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 transition: color .3s var(--n-bezier);
 position: relative;
 padding: var(--n-title-padding);
 color: var(--n-title-text-color);
 `,[n("header-main",`
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 color: var(--n-title-text-color);
 `),n("header-extra",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),h("collapse-item-arrow",`
 display: flex;
 transition:
 transform .15s var(--n-bezier),
 color .3s var(--n-bezier);
 font-size: 18px;
 color: var(--n-arrow-color);
 `)])])]),ce=Object.assign(Object.assign({},L.props),{defaultExpandedNames:{type:[Array,String],default:null},expandedNames:[Array,String],arrowPlacement:{type:String,default:"left"},accordion:{type:Boolean,default:!1},displayDirective:{type:String,default:"if"},triggerAreas:{type:Array,default:()=>["main","extra","arrow"]},onItemHeaderClick:[Function,Array],"onUpdate:expandedNames":[Function,Array],onUpdateExpandedNames:[Function,Array],onExpandedNamesChange:{type:[Function,Array],validator:()=>!0,default:void 0}}),j=G("n-collapse"),ue=k({name:"Collapse",props:ce,slots:Object,setup(e,{slots:i}){const{mergedClsPrefixRef:t,inlineThemeDisabled:s,mergedRtlRef:d}=D(e),r=q(e.defaultExpandedNames),f=N(()=>e.expandedNames),v=W(f,r),w=L("Collapse","-collapse",de,Z,e,t);function c(p){const{"onUpdate:expandedNames":l,onUpdateExpandedNames:m,onExpandedNamesChange:b}=e;m&&S(m,p),l&&S(l,p),b&&S(b,p),r.value=p}function g(p){const{onItemHeaderClick:l}=e;l&&S(l,p)}function a(p,l,m){const{accordion:b}=e,{value:E}=v;if(b)p?(c([l]),g({name:l,expanded:!0,event:m})):(c([]),g({name:l,expanded:!1,event:m}));else if(!Array.isArray(E))c([l]),g({name:l,expanded:!0,event:m});else{const y=E.slice(),I=y.findIndex(z=>l===z);~I?(y.splice(I,1),c(y),g({name:l,expanded:!1,event:m})):(y.push(l),c(y),g({name:l,expanded:!0,event:m}))}}Q(j,{props:e,mergedClsPrefixRef:t,expandedNamesRef:v,slots:i,toggleItem:a});const u=_("Collapse",d,t),R=N(()=>{const{common:{cubicBezierEaseInOut:p},self:{titleFontWeight:l,dividerColor:m,titlePadding:b,titleTextColor:E,titleTextColorDisabled:y,textColor:I,arrowColor:z,fontSize:B,titleFontSize:F,arrowColorDisabled:U,itemMargin:H}}=w.value;return{"--n-font-size":B,"--n-bezier":p,"--n-text-color":I,"--n-divider-color":m,"--n-title-padding":b,"--n-title-font-size":F,"--n-title-text-color":E,"--n-title-text-color-disabled":y,"--n-title-font-weight":l,"--n-arrow-color":z,"--n-arrow-color-disabled":U,"--n-item-margin":H}}),C=s?K("collapse",void 0,R,e):void 0;return{rtlEnabled:u,mergedTheme:w,mergedClsPrefix:t,cssVars:s?void 0:R,themeClass:C==null?void 0:C.themeClass,onRender:C==null?void 0:C.onRender}},render(){var e;return(e=this.onRender)===null||e===void 0||e.call(this),o("div",{class:[`${this.mergedClsPrefix}-collapse`,this.rtlEnabled&&`${this.mergedClsPrefix}-collapse--rtl`,this.themeClass],style:this.cssVars},this.$slots)}}),pe=k({name:"CollapseItemContent",props:{displayDirective:{type:String,required:!0},show:Boolean,clsPrefix:{type:String,required:!0}},setup(e){return{onceTrue:Y(T(e,"show"))}},render(){return o(J,null,{default:()=>{const{show:e,displayDirective:i,onceTrue:t,clsPrefix:s}=this,d=i==="show"&&t,r=o("div",{class:`${s}-collapse-item__content-wrapper`},o("div",{class:`${s}-collapse-item__content-inner`},this.$slots));return d?X(r,[[ee,e]]):e?r:null}})}}),me={title:String,name:[String,Number],disabled:Boolean,displayDirective:String},fe=k({name:"CollapseItem",props:me,setup(e){const{mergedRtlRef:i}=D(e),t=re(),s=te(()=>{var a;return(a=e.name)!==null&&a!==void 0?a:t}),d=oe(j);d||le("collapse-item","`n-collapse-item` must be placed inside `n-collapse`.");const{expandedNamesRef:r,props:f,mergedClsPrefixRef:v,slots:w}=d,c=N(()=>{const{value:a}=r;if(Array.isArray(a)){const{value:u}=s;return!~a.findIndex(R=>R===u)}else if(a){const{value:u}=s;return u!==a}return!0});return{rtlEnabled:_("Collapse",i,v),collapseSlots:w,randomName:t,mergedClsPrefix:v,collapsed:c,triggerAreas:T(f,"triggerAreas"),mergedDisplayDirective:N(()=>{const{displayDirective:a}=e;return a||f.displayDirective}),arrowPlacement:N(()=>f.arrowPlacement),handleClick(a){let u="main";A(a,"arrow")&&(u="arrow"),A(a,"extra")&&(u="extra"),f.triggerAreas.includes(u)&&d&&!e.disabled&&d.toggleItem(c.value,s.value,a)}}},render(){const{collapseSlots:e,$slots:i,arrowPlacement:t,collapsed:s,mergedDisplayDirective:d,mergedClsPrefix:r,disabled:f,triggerAreas:v}=this,w=$(i.header,{collapsed:s},()=>[this.title]),c=i["header-extra"]||e["header-extra"],g=i.arrow||e.arrow;return o("div",{class:[`${r}-collapse-item`,`${r}-collapse-item--${t}-arrow-placement`,f&&`${r}-collapse-item--disabled`,!s&&`${r}-collapse-item--active`,v.map(a=>`${r}-collapse-item--trigger-area-${a}`)]},o("div",{class:[`${r}-collapse-item__header`,!s&&`${r}-collapse-item__header--active`]},o("div",{class:`${r}-collapse-item__header-main`,onClick:this.handleClick},t==="right"&&w,o("div",{class:`${r}-collapse-item-arrow`,key:this.rtlEnabled?0:1,"data-arrow":!0},$(g,{collapsed:s},()=>[o(se,{clsPrefix:r},{default:()=>this.rtlEnabled?o(ne,null):o(ie,null)})])),t==="left"&&w),ae(c,{collapsed:s},a=>o("div",{class:`${r}-collapse-item__header-extra`,onClick:this.handleClick,"data-extra":!0},a))),o(pe,{clsPrefix:r,displayDirective:d,show:!s},i))}});/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=P("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xe=P("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=P("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=P("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);export{ge as B,xe as C,ve as L,ue as N,we as S,fe as a,A as h};
