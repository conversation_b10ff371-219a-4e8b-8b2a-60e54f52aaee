import{F as C,y as w,O as m,z as f,d as x,aA as $,h as c,m as k,G as z,H as g,c as p,J as _}from"./index-xOKHWKBe.js";const D=/^(\d|\.)+$/,y=/(\d|\.)+/;function M(e,{c:t=1,offset:o=0,attachPx:r=!0}={}){if(typeof e=="number"){const n=(e+o)*t;return n===0?"0":`${n}px`}else if(typeof e=="string")if(D.test(e)){const n=(Number(e)+o)*t;return r?n===0?"0":`${n}px`:`${n}`}else{const n=y.exec(e);return n?e.replace(y,String((Number(n[0])+o)*t)):e}return e}function S(e){const{textColorBase:t,opacity1:o,opacity2:r,opacity3:n,opacity4:i,opacity5:s}=e;return{color:t,opacity1Depth:o,opacity2Depth:r,opacity3Depth:n,opacity4Depth:i,opacity5Depth:s}}const R={common:C,self:S},H=w("icon",`
 height: 1em;
 width: 1em;
 line-height: 1em;
 text-align: center;
 display: inline-block;
 position: relative;
 fill: currentColor;
`,[m("color-transition",{transition:"color .3s var(--n-bezier)"}),m("depth",{color:"var(--n-color)"},[f("svg",{opacity:"var(--n-opacity)",transition:"opacity .3s var(--n-bezier)"})]),f("svg",{height:"1em",width:"1em"})]),L=Object.assign(Object.assign({},g.props),{depth:[String,Number],size:[Number,String],color:String,component:[Object,Function]}),j=x({_n_icon__:!0,name:"Icon",inheritAttrs:!1,props:L,setup(e){const{mergedClsPrefixRef:t,inlineThemeDisabled:o}=z(e),r=g("Icon","-icon",H,R,e,t),n=p(()=>{const{depth:s}=e,{common:{cubicBezierEaseInOut:a},self:l}=r.value;if(s!==void 0){const{color:u,[`opacity${s}Depth`]:b}=l;return{"--n-bezier":a,"--n-color":u,"--n-opacity":b}}return{"--n-bezier":a,"--n-color":"","--n-opacity":""}}),i=o?_("icon",p(()=>`${e.depth||"d"}`),n,e):void 0;return{mergedClsPrefix:t,mergedStyle:p(()=>{const{size:s,color:a}=e;return{fontSize:M(s),color:a}}),cssVars:o?void 0:n,themeClass:i==null?void 0:i.themeClass,onRender:i==null?void 0:i.onRender}},render(){var e;const{$parent:t,depth:o,mergedClsPrefix:r,component:n,onRender:i,themeClass:s}=this;return!((e=t==null?void 0:t.$options)===null||e===void 0)&&e._n_icon__&&$("icon","don't wrap `n-icon` inside `n-icon`"),i==null||i(),c("i",k(this.$attrs,{role:"img",class:[`${r}-icon`,s,{[`${r}-icon--depth`]:o,[`${r}-icon--color-transition`]:o!==void 0}],style:[this.cssVars,this.mergedStyle]}),n?c(n):this.$slots)}});/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const v=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),N=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(t,o,r)=>r?r.toUpperCase():o.toLowerCase()),A=e=>{const t=N(e);return t.charAt(0).toUpperCase()+t.slice(1)},I=(...e)=>e.filter((t,o,r)=>!!t&&t.trim()!==""&&r.indexOf(t)===o).join(" ").trim();/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const O=({size:e,strokeWidth:t=2,absoluteStrokeWidth:o,color:r,iconNode:n,name:i,class:s,...a},{slots:l})=>c("svg",{...d,width:e||d.width,height:e||d.height,stroke:r||d.stroke,"stroke-width":o?Number(t)*24/Number(e):t,class:I("lucide",...i?[`lucide-${v(A(i))}-icon`,`lucide-${v(i)}`]:["lucide-icon"]),...a},[...n.map(u=>c(...u)),...l.default?[l.default()]:[]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const h=(e,t)=>(o,{slots:r})=>c(O,{...o,iconNode:t,name:e},r);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=h("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const V=h("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const F=h("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]);export{B as D,V as F,j as N,F as S,h as c,M as f};
