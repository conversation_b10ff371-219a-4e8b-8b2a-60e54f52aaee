import{aO as d,aR as u,aT as r,aV as t,aW as l,aX as n,aY as s,b3 as o,aU as m,aZ as a,b6 as v}from"./index-BNcvR5C7.js";import{S as c,B as _,L as f,N as h,a as i,C as x}from"./search-gDejeVrZ.js";import{S as y,F as g,D as z}from"./star-CvibHW9y.js";/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const b=d("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const p=d("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=d("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const I=d("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),k={class:"help-view"},V={class:"help-container"},w={class:"help-content"},A={class:"help-section"},D={class:"feature-list"},N={class:"feature-item"},S={class:"feature-icon"},M={class:"feature-item"},E={class:"feature-icon"},B={class:"feature-item"},L={class:"feature-icon"},q={class:"feature-item"},F={class:"feature-icon"},H={class:"feature-item"},G={class:"feature-icon"},T={class:"feature-item"},Z={class:"feature-icon"},j={class:"help-section"},O={class:"help-section"},Q={class:"contact-content"},R={class:"contact-methods"},U={class:"contact-item"},W={class:"contact-item"},X={class:"version-info"},Y={__name:"HelpView",setup(J){return(K,e)=>(m(),r("div",k,[t("div",V,[e[17]||(e[17]=t("div",{class:"help-header"},[t("h1",{class:"help-title"},"帮助文档"),t("p",{class:"help-subtitle"},"了解如何使用漏洞情报分析平台")],-1)),t("div",w,[l(s(o),{title:"快速开始",size:"large"},{"header-extra":n(()=>[l(s(a),{component:s(I)},null,8,["component"])]),default:n(()=>[e[0]||(e[0]=t("div",{class:"help-section"},[t("div",{class:"step-list"},[t("div",{class:"step-item"},[t("div",{class:"step-number"},"1"),t("div",{class:"step-content"},[t("h3",null,"搜索漏洞"),t("p",null,"在左侧搜索框中输入 CVE-ID、产品名或关键词，点击搜索按钮开始查找漏洞。")])]),t("div",{class:"step-item"},[t("div",{class:"step-number"},"2"),t("div",{class:"step-content"},[t("h3",null,"筛选结果"),t("p",null,"使用严重等级筛选器和排序功能，快速找到您关注的漏洞。")])]),t("div",{class:"step-item"},[t("div",{class:"step-number"},"3"),t("div",{class:"step-content"},[t("h3",null,"查看详情"),t("p",null,"点击搜索结果中的漏洞，在右侧查看详细信息和 AI 分析。")])]),t("div",{class:"step-item"},[t("div",{class:"step-number"},"4"),t("div",{class:"step-content"},[t("h3",null,"收藏管理"),t("p",null,"将重要漏洞添加到收藏夹，并添加个人备注进行管理。")])])])],-1))]),_:1,__:[0]}),l(s(o),{title:"搜索语法",size:"large"},{"header-extra":n(()=>[l(s(a),{component:s(c)},null,8,["component"])]),default:n(()=>[e[1]||(e[1]=t("div",{class:"help-section"},[t("div",{class:"syntax-grid"},[t("div",{class:"syntax-item"},[t("div",{class:"syntax-example"},"CVE-2021-44228"),t("div",{class:"syntax-description"},"精确搜索特定的 CVE ID")]),t("div",{class:"syntax-item"},[t("div",{class:"syntax-example"},"log4j"),t("div",{class:"syntax-description"},"搜索包含关键词的漏洞")]),t("div",{class:"syntax-item"},[t("div",{class:"syntax-example"},"apache"),t("div",{class:"syntax-description"},"搜索特定厂商的漏洞")]),t("div",{class:"syntax-item"},[t("div",{class:"syntax-example"},"remote code execution"),t("div",{class:"syntax-description"},"搜索特定类型的漏洞")])]),t("div",{class:"search-tips"},[t("h4",null,"搜索技巧"),t("ul",null,[t("li",null,"使用空格分隔多个关键词进行组合搜索"),t("li",null,"搜索不区分大小写"),t("li",null,"支持中英文混合搜索"),t("li",null,"可以搜索漏洞描述中的内容")])])],-1))]),_:1,__:[1]}),l(s(o),{title:"功能介绍",size:"large"},{"header-extra":n(()=>[l(s(a),{component:s(C)},null,8,["component"])]),default:n(()=>[t("div",A,[t("div",D,[t("div",N,[t("div",S,[l(s(a),{component:s(c),size:"24",color:"#18a058"},null,8,["component"])]),e[2]||(e[2]=t("div",{class:"feature-content"},[t("h4",null,"智能搜索"),t("p",null,"支持多维度搜索，包括 CVE-ID、产品名、厂商名、漏洞描述等。提供严重等级筛选和多种排序方式。")],-1))]),t("div",M,[t("div",E,[l(s(a),{component:s(y),size:"24",color:"#f0a020"},null,8,["component"])]),e[3]||(e[3]=t("div",{class:"feature-content"},[t("h4",null,"收藏管理"),t("p",null,"创建自定义收藏夹，分类管理重要漏洞。支持批量收藏和收藏夹导出功能。")],-1))]),t("div",B,[t("div",L,[l(s(a),{component:s(g),size:"24",color:"#2080f0"},null,8,["component"])]),e[4]||(e[4]=t("div",{class:"feature-content"},[t("h4",null,"备注系统"),t("p",null,"为每个漏洞添加个人备注，支持 Markdown 格式，实时预览编辑效果。")],-1))]),t("div",q,[t("div",F,[l(s(a),{component:s(_),size:"24",color:"#9333ea"},null,8,["component"])]),e[5]||(e[5]=t("div",{class:"feature-content"},[t("h4",null,"AI 分析"),t("p",null,"获取 AI 驱动的漏洞深度分析，包括影响范围、修复建议和安全评估。")],-1))]),t("div",H,[t("div",G,[l(s(a),{component:s(f),size:"24",color:"#d03050"},null,8,["component"])]),e[6]||(e[6]=t("div",{class:"feature-content"},[t("h4",null,"智能翻译"),t("p",null,"一键翻译英文漏洞描述为中文，消除语言障碍，提高分析效率。")],-1))]),t("div",T,[t("div",Z,[l(s(a),{component:s(z),size:"24",color:"#18a058"},null,8,["component"])]),e[7]||(e[7]=t("div",{class:"feature-content"},[t("h4",null,"数据导出"),t("p",null,"支持收藏夹和备注数据的导出备份，以及数据的导入恢复功能。")],-1))])])])]),_:1}),l(s(o),{title:"常见问题",size:"large"},{"header-extra":n(()=>[l(s(a),{component:s(x)},null,8,["component"])]),default:n(()=>[t("div",j,[l(s(h),null,{default:n(()=>[l(s(i),{title:"如何创建新的收藏夹？",name:"1"},{default:n(()=>e[8]||(e[8]=[t("p",null,'在左侧收藏夹区域，点击标题旁的 "+" 按钮，输入收藏夹名称即可创建。',-1)])),_:1,__:[8]}),l(s(i),{title:"如何批量收藏漏洞？",name:"2"},{default:n(()=>e[9]||(e[9]=[t("p",null,"先勾选要添加到的收藏夹，然后在漏洞详情页面点击收藏按钮，漏洞会自动添加到所有勾选的收藏夹中。",-1)])),_:1,__:[9]}),l(s(i),{title:"备注数据会丢失吗？",name:"3"},{default:n(()=>e[10]||(e[10]=[t("p",null,"所有数据都保存在浏览器本地存储中，不会上传到服务器。建议定期导出备份重要数据。",-1)])),_:1,__:[10]}),l(s(i),{title:"AI 分析准确吗？",name:"4"},{default:n(()=>e[11]||(e[11]=[t("p",null,"AI 分析基于大语言模型，提供参考性建议。请结合实际情况和专业判断使用分析结果。",-1)])),_:1,__:[11]}),l(s(i),{title:"支持哪些浏览器？",name:"5"},{default:n(()=>e[12]||(e[12]=[t("p",null,"推荐使用 Chrome、Firefox、Safari、Edge 等现代浏览器的最新版本。",-1)])),_:1,__:[12]})]),_:1})])]),_:1}),l(s(o),{title:"联系支持",size:"large"},{"header-extra":n(()=>[l(s(a),{component:s(p)},null,8,["component"])]),default:n(()=>[t("div",O,[t("div",Q,[e[16]||(e[16]=t("p",null,"如果您在使用过程中遇到问题或有改进建议，欢迎联系我们：",-1)),t("div",R,[t("div",U,[l(s(a),{component:s(p)},null,8,["component"]),e[13]||(e[13]=t("span",null,"邮箱：<EMAIL>",-1))]),t("div",W,[l(s(a),{component:s(b)},null,8,["component"]),e[14]||(e[14]=t("span",null,"GitHub：github.com/cve-platform",-1))])]),t("div",X,[e[15]||(e[15]=t("p",null,"当前版本：v1.0.0",-1)),t("p",null,"更新时间："+v(new Date().toLocaleDateString("zh-CN")),1)])])])]),_:1})])])]))}},st=u(Y,[["__scopeId","data-v-b3ea7d91"]]);export{st as default};
