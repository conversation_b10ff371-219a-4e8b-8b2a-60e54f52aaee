.header-container[data-v-2a05bb0c]{display:flex;justify-content:space-between;align-items:center;padding:0 24px;background:#fff;border-bottom:1px solid #e0e0e6;height:64px}.header-tabs[data-v-2a05bb0c]{flex:1}.header-info[data-v-2a05bb0c]{display:flex;align-items:center;gap:16px}.app-title[data-v-2a05bb0c]{font-size:16px;font-weight:600;color:#333}.loading-indicator[data-v-2a05bb0c]{display:flex;align-items:center}[data-v-2a05bb0c] .n-tabs-nav{border-bottom:none}[data-v-2a05bb0c] .n-tabs-tab{font-size:16px;font-weight:500;padding:0 24px}[data-v-2a05bb0c] .n-tabs-tab:hover{color:#18a058}[data-v-2a05bb0c] .n-tabs-tab--active{color:#18a058;font-weight:600}[data-v-2a05bb0c] .n-tabs-bar{background-color:#18a058}.footer-container[data-v-bdd50eb9]{flex-shrink:0;background:#f5f5f5;border-top:1px solid #e0e0e6;padding:12px 24px}.footer-content[data-v-bdd50eb9]{display:flex;justify-content:space-between;align-items:center;max-width:1200px;margin:0 auto}.copyright[data-v-bdd50eb9]{font-size:12px;color:#666}.footer-links[data-v-bdd50eb9]{display:flex;align-items:center;gap:12px;font-size:12px;color:#999}.version[data-v-bdd50eb9]{color:#666}.dev-mode[data-v-bdd50eb9]{background:#ff6b35;color:#fff;padding:2px 6px;border-radius:4px;font-size:10px;font-weight:500}*{box-sizing:border-box}html,body{margin:0;padding:0;height:100%;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Fira Sans,Droid Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;background-color:#f5f5f5}#app{height:100vh;overflow:hidden}.app{display:flex;flex-direction:column;height:100vh;overflow:hidden}.app-header{flex-shrink:0;z-index:100}.app-main{flex:1;overflow:hidden}.app-footer{flex-shrink:0}::-webkit-scrollbar{width:6px;height:6px}::-webkit-scrollbar-track{background:#f1f1f1;border-radius:3px}::-webkit-scrollbar-thumb{background:#c1c1c1;border-radius:3px}::-webkit-scrollbar-thumb:hover{background:#a8a8a8}code,pre,.code{font-family:Monaco,Menlo,Ubuntu Mono,Consolas,source-code-pro,monospace}a{color:#18a058;text-decoration:none}a:hover{text-decoration:underline}::-moz-selection{background-color:#18a05833}::selection{background-color:#18a05833}:focus-visible{outline:2px solid #18a058;outline-offset:2px}.disabled{opacity:.6;cursor:not-allowed}.gap-2{gap:8px}.gap-4{gap:16px}.gap-6{gap:24px}.p-2{padding:8px}.p-4{padding:16px}.p-6{padding:24px}.m-2{margin:8px}.m-4{margin:16px}.m-6{margin:24px}@media (max-width: 768px){.md\:hidden{display:none}.md\:flex{display:flex}}@media (max-width: 480px){.sm\:hidden{display:none}.sm\:flex{display:flex}}/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */@layer properties{@supports ((-webkit-hyphens:none) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-border-style:solid;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial}}}.visible{visibility:visible}.flex{display:flex}.h-full{height:100%}.w-full{width:100%}.flex-shrink{flex-shrink:1}.border-collapse{border-collapse:collapse}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.resize{resize:both}.flex-col{flex-direction:column}.flex-wrap{flex-wrap:wrap}.items-center{align-items:center}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.border{border-style:var(--tw-border-style);border-width:1px}.text-center{text-align:center}.text-left{text-align:left}.text-right{text-align:right}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,ease);transition-duration:var(--tw-duration,0s)}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}
