import{d as k,h as n,F as U,y as f,O as x,z as M,A as s,P as V,aj as O,G as D,r as q,c as N,a7 as K,H as T,W as _,J as Z,a4 as G,p as J,aa as S,a3 as Q,an as X,bV as Y,t as L,ao as ee,a5 as $,bW as te,ai as re,u as ae,aB as oe,i as le,E as ne}from"./index-xOKHWKBe.js";import{c as P}from"./star-Nx5axgZH.js";function A(e,i){let{target:r}=e;for(;r;){if(r.dataset&&r.dataset[i]!==void 0)return!0;r=r.parentElement}return!1}const se=k({name:"ChevronLeft",render(){return n("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M10.3536 3.14645C10.5488 3.34171 10.5488 3.65829 10.3536 3.85355L6.20711 8L10.3536 12.1464C10.5488 12.3417 10.5488 12.6583 10.3536 12.8536C10.1583 13.0488 9.84171 13.0488 9.64645 12.8536L5.14645 8.35355C4.95118 8.15829 4.95118 7.84171 5.14645 7.64645L9.64645 3.14645C9.84171 2.95118 10.1583 2.95118 10.3536 3.14645Z",fill:"currentColor"}))}}),ie=k({name:"ChevronRight",render(){return n("svg",{viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},n("path",{d:"M5.64645 3.14645C5.45118 3.34171 5.45118 3.65829 5.64645 3.85355L9.79289 8L5.64645 12.1464C5.45118 12.3417 5.45118 12.6583 5.64645 12.8536C5.84171 13.0488 6.15829 13.0488 6.35355 12.8536L10.8536 8.35355C11.0488 8.15829 11.0488 7.84171 10.8536 7.64645L6.35355 3.14645C6.15829 2.95118 5.84171 2.95118 5.64645 3.14645Z",fill:"currentColor"}))}});function de(e){const{fontWeight:i,textColor1:r,textColor2:o,textColorDisabled:d,dividerColor:t,fontSize:c}=e;return{titleFontSize:c,titleFontWeight:i,dividerColor:t,titleTextColor:r,titleTextColorDisabled:d,fontSize:c,textColor:o,arrowColor:o,arrowColorDisabled:d,itemMargin:"16px 0 0 0",titlePadding:"16px 0 0 0"}}const ce={common:U,self:de},pe=f("collapse","width: 100%;",[f("collapse-item",`
 font-size: var(--n-font-size);
 color: var(--n-text-color);
 transition:
 color .3s var(--n-bezier),
 border-color .3s var(--n-bezier);
 margin: var(--n-item-margin);
 `,[x("disabled",[s("header","cursor: not-allowed;",[s("header-main",`
 color: var(--n-title-text-color-disabled);
 `),f("collapse-item-arrow",`
 color: var(--n-arrow-color-disabled);
 `)])]),f("collapse-item","margin-left: 32px;"),M("&:first-child","margin-top: 0;"),M("&:first-child >",[s("header","padding-top: 0;")]),x("left-arrow-placement",[s("header",[f("collapse-item-arrow","margin-right: 4px;")])]),x("right-arrow-placement",[s("header",[f("collapse-item-arrow","margin-left: 4px;")])]),s("content-wrapper",[s("content-inner","padding-top: 16px;"),O({duration:"0.15s"})]),x("active",[s("header",[x("active",[f("collapse-item-arrow","transform: rotate(90deg);")])])]),M("&:not(:first-child)","border-top: 1px solid var(--n-divider-color);"),V("disabled",[x("trigger-area-main",[s("header",[s("header-main","cursor: pointer;"),f("collapse-item-arrow","cursor: default;")])]),x("trigger-area-arrow",[s("header",[f("collapse-item-arrow","cursor: pointer;")])]),x("trigger-area-extra",[s("header",[s("header-extra","cursor: pointer;")])])]),s("header",`
 font-size: var(--n-title-font-size);
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 transition: color .3s var(--n-bezier);
 position: relative;
 padding: var(--n-title-padding);
 color: var(--n-title-text-color);
 `,[s("header-main",`
 display: flex;
 flex-wrap: nowrap;
 align-items: center;
 font-weight: var(--n-title-font-weight);
 transition: color .3s var(--n-bezier);
 flex: 1;
 color: var(--n-title-text-color);
 `),s("header-extra",`
 display: flex;
 align-items: center;
 transition: color .3s var(--n-bezier);
 color: var(--n-text-color);
 `),f("collapse-item-arrow",`
 display: flex;
 transition:
 transform .15s var(--n-bezier),
 color .3s var(--n-bezier);
 font-size: 18px;
 color: var(--n-arrow-color);
 `)])])]),me=Object.assign(Object.assign({},T.props),{defaultExpandedNames:{type:[Array,String],default:null},expandedNames:[Array,String],arrowPlacement:{type:String,default:"left"},accordion:{type:Boolean,default:!1},displayDirective:{type:String,default:"if"},triggerAreas:{type:Array,default:()=>["main","extra","arrow"]},onItemHeaderClick:[Function,Array],"onUpdate:expandedNames":[Function,Array],onUpdateExpandedNames:[Function,Array],onExpandedNamesChange:{type:[Function,Array],validator:()=>!0,default:void 0}}),j=G("n-collapse"),xe=k({name:"Collapse",props:me,slots:Object,setup(e,{slots:i}){const{mergedClsPrefixRef:r,inlineThemeDisabled:o,mergedRtlRef:d}=D(e),t=q(e.defaultExpandedNames),c=N(()=>e.expandedNames),v=K(c,t),C=T("Collapse","-collapse",pe,ce,e,r);function p(m){const{"onUpdate:expandedNames":l,onUpdateExpandedNames:h,onExpandedNamesChange:b}=e;h&&S(h,m),l&&S(l,m),b&&S(b,m),t.value=m}function g(m){const{onItemHeaderClick:l}=e;l&&S(l,m)}function a(m,l,h){const{accordion:b}=e,{value:R}=v;if(b)m?(p([l]),g({name:l,expanded:!0,event:h})):(p([]),g({name:l,expanded:!1,event:h}));else if(!Array.isArray(R))p([l]),g({name:l,expanded:!0,event:h});else{const w=R.slice(),I=w.findIndex(z=>l===z);~I?(w.splice(I,1),p(w),g({name:l,expanded:!1,event:h})):(w.push(l),p(w),g({name:l,expanded:!0,event:h}))}}J(j,{props:e,mergedClsPrefixRef:r,expandedNamesRef:v,slots:i,toggleItem:a});const u=_("Collapse",d,r),E=N(()=>{const{common:{cubicBezierEaseInOut:m},self:{titleFontWeight:l,dividerColor:h,titlePadding:b,titleTextColor:R,titleTextColorDisabled:w,textColor:I,arrowColor:z,fontSize:B,titleFontSize:F,arrowColorDisabled:W,itemMargin:H}}=C.value;return{"--n-font-size":B,"--n-bezier":m,"--n-text-color":I,"--n-divider-color":h,"--n-title-padding":b,"--n-title-font-size":F,"--n-title-text-color":R,"--n-title-text-color-disabled":w,"--n-title-font-weight":l,"--n-arrow-color":z,"--n-arrow-color-disabled":W,"--n-item-margin":H}}),y=o?Z("collapse",void 0,E,e):void 0;return{rtlEnabled:u,mergedTheme:C,mergedClsPrefix:r,cssVars:o?void 0:E,themeClass:y==null?void 0:y.themeClass,onRender:y==null?void 0:y.onRender}},render(){var e;return(e=this.onRender)===null||e===void 0||e.call(this),n("div",{class:[`${this.mergedClsPrefix}-collapse`,this.rtlEnabled&&`${this.mergedClsPrefix}-collapse--rtl`,this.themeClass],style:this.cssVars},this.$slots)}}),he=k({name:"CollapseItemContent",props:{displayDirective:{type:String,required:!0},show:Boolean,clsPrefix:{type:String,required:!0}},setup(e){return{onceTrue:Y(L(e,"show"))}},render(){return n(Q,null,{default:()=>{const{show:e,displayDirective:i,onceTrue:r,clsPrefix:o}=this,d=i==="show"&&r,t=n("div",{class:`${o}-collapse-item__content-wrapper`},n("div",{class:`${o}-collapse-item__content-inner`},this.$slots));return d?X(t,[[ee,e]]):e?t:null}})}}),fe={title:String,name:[String,Number],disabled:Boolean,displayDirective:String},ve=k({name:"CollapseItem",props:fe,setup(e){const{mergedRtlRef:i}=D(e),r=re(),o=ae(()=>{var a;return(a=e.name)!==null&&a!==void 0?a:r}),d=le(j);d||oe("collapse-item","`n-collapse-item` must be placed inside `n-collapse`.");const{expandedNamesRef:t,props:c,mergedClsPrefixRef:v,slots:C}=d,p=N(()=>{const{value:a}=t;if(Array.isArray(a)){const{value:u}=o;return!~a.findIndex(E=>E===u)}else if(a){const{value:u}=o;return u!==a}return!0});return{rtlEnabled:_("Collapse",i,v),collapseSlots:C,randomName:r,mergedClsPrefix:v,collapsed:p,triggerAreas:L(c,"triggerAreas"),mergedDisplayDirective:N(()=>{const{displayDirective:a}=e;return a||c.displayDirective}),arrowPlacement:N(()=>c.arrowPlacement),handleClick(a){let u="main";A(a,"arrow")&&(u="arrow"),A(a,"extra")&&(u="extra"),c.triggerAreas.includes(u)&&d&&!e.disabled&&d.toggleItem(p.value,o.value,a)}}},render(){const{collapseSlots:e,$slots:i,arrowPlacement:r,collapsed:o,mergedDisplayDirective:d,mergedClsPrefix:t,disabled:c,triggerAreas:v}=this,C=$(i.header,{collapsed:o},()=>[this.title]),p=i["header-extra"]||e["header-extra"],g=i.arrow||e.arrow;return n("div",{class:[`${t}-collapse-item`,`${t}-collapse-item--${r}-arrow-placement`,c&&`${t}-collapse-item--disabled`,!o&&`${t}-collapse-item--active`,v.map(a=>`${t}-collapse-item--trigger-area-${a}`)]},n("div",{class:[`${t}-collapse-item__header`,!o&&`${t}-collapse-item__header--active`]},n("div",{class:`${t}-collapse-item__header-main`,onClick:this.handleClick},r==="right"&&C,n("div",{class:`${t}-collapse-item-arrow`,key:this.rtlEnabled?0:1,"data-arrow":!0},$(g,{collapsed:o},()=>[n(ne,{clsPrefix:t},{default:()=>this.rtlEnabled?n(se,null):n(ie,null)})])),r==="left"&&C),te(p,{collapsed:o},a=>n("div",{class:`${t}-collapse-item__header-extra`,onClick:this.handleClick,"data-extra":!0},a))),n(he,{clsPrefix:t,displayDirective:d,show:!o},i))}});/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ce=P("bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=P("circle-question-mark",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=P("languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]);/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const be=P("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);export{Ce as B,we as C,ye as L,xe as N,be as S,ve as a,A as h};
