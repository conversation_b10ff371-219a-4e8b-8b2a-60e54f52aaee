{"name": "cve-platform", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:unit": "vitest run tests/unit", "test:integration": "vitest run tests/integration"}, "dependencies": {"@vueuse/core": "^13.4.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "dompurify": "^3.2.6", "lucide-vue-next": "^0.522.0", "marked": "^15.0.12", "naive-ui": "^2.42.0", "pinia": "^3.0.3", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "vue": "^3.5.13", "vue-router": "^4.5.1"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/vue": "^8.1.0", "@types/dompurify": "^3.0.5", "@vitejs/plugin-vue": "^5.2.3", "@vue/test-utils": "^2.4.6", "jsdom": "^26.1.0", "vite": "^6.3.5", "vitest": "^3.2.4"}}