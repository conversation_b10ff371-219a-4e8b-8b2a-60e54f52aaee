import { ComponentInternalInstance, VNode } from 'vue';
import { FindAllComponentsSelector } from '../types';
/**
 * Detect whether a selector matches a VNode
 * @param node
 * @param selector
 * @return {boolean | ((value: any) => boolean)}
 */
export declare function matches(node: VNode, rawSelector: FindAllComponentsSelector): boolean;
export declare function find(root: VNode, selector: FindAllComponentsSelector): ComponentInternalInstance[];
