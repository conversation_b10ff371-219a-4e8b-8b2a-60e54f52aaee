export declare const RouterLinkStub: import("vue").DefineComponent<{
    to: {
        type: (ObjectConstructor | StringConstructor)[];
        required: true;
    };
    custom: {
        type: BooleanConstructor;
        default: boolean;
    };
}, unknown, unknown, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    to: {
        type: (ObjectConstructor | StringConstructor)[];
        required: true;
    };
    custom: {
        type: BooleanConstructor;
        default: boolean;
    };
}>>, {
    custom: boolean;
}, {}>;
