{"name": "@jridgewell/trace-mapping", "version": "0.3.25", "description": "Trace the original position through a source map", "keywords": ["source", "map"], "main": "dist/trace-mapping.umd.js", "module": "dist/trace-mapping.mjs", "types": "dist/types/trace-mapping.d.ts", "files": ["dist"], "exports": {".": [{"types": "./dist/types/trace-mapping.d.ts", "browser": "./dist/trace-mapping.umd.js", "require": "./dist/trace-mapping.umd.js", "import": "./dist/trace-mapping.mjs"}, "./dist/trace-mapping.umd.js"], "./package.json": "./package.json"}, "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/jridgewell/trace-mapping.git"}, "license": "MIT", "scripts": {"benchmark": "run-s build:rollup benchmark:*", "benchmark:install": "cd benchmark && npm install", "benchmark:only": "node --expose-gc benchmark/index.mjs", "build": "run-s -n build:*", "build:rollup": "rollup -c rollup.config.mjs", "build:ts": "tsc --project tsconfig.build.json", "lint": "run-s -n lint:*", "lint:prettier": "npm run test:lint:prettier -- --write", "lint:ts": "npm run test:lint:ts -- --fix", "prebuild": "rm -rf dist", "prepublishOnly": "npm run preversion", "preversion": "run-s test build", "test": "run-s -n test:lint test:only", "test:debug": "mocha --inspect-brk", "test:lint": "run-s -n test:lint:*", "test:lint:prettier": "prettier --check '{src,test}/**/*.ts' '**/*.md'", "test:lint:ts": "eslint '{src,test}/**/*.ts'", "test:only": "c8 mocha", "test:watch": "mocha --watch"}, "devDependencies": {"@rollup/plugin-typescript": "11.1.6", "@types/mocha": "10.0.6", "@types/node": "20.11.20", "@typescript-eslint/eslint-plugin": "6.18.1", "@typescript-eslint/parser": "6.18.1", "benchmark": "2.1.4", "c8": "9.0.0", "esbuild": "0.19.11", "eslint": "8.56.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-no-only-tests": "3.1.0", "mocha": "10.3.0", "npm-run-all": "4.1.5", "prettier": "3.1.1", "rollup": "4.9.4", "tsx": "4.7.0", "typescript": "5.3.3"}, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}