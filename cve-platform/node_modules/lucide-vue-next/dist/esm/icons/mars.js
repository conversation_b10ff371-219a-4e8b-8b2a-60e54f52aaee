/**
 * @license lucide-vue-next v0.522.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const Mars = createLucideIcon("mars", [
  ["path", { d: "M16 3h5v5", key: "1806ms" }],
  ["path", { d: "m21 3-6.75 6.75", key: "pv0uzu" }],
  ["circle", { cx: "10", cy: "14", r: "6", key: "1qwbdc" }]
]);

export { Mars as default };
//# sourceMappingURL=mars.js.map
