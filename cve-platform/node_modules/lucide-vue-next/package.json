{"name": "lucide-vue-next", "version": "0.522.0", "author": "<PERSON>", "description": "A Lucide icon library package for Vue 3 applications.", "license": "ISC", "homepage": "https://lucide.dev", "bugs": "https://github.com/lucide-icons/lucide/issues", "repository": {"type": "git", "url": "https://github.com/lucide-icons/lucide.git", "directory": "packages/lucide-vue-next"}, "keywords": ["Lucide", "<PERSON><PERSON>", "<PERSON><PERSON>", "Icons", "Icon", "SVG", "Feather Icons", "Fontawesome", "Font Awesome"], "amdName": "lucide-vue-next", "source": "build/lucide-vue-next.js", "main": "dist/cjs/lucide-vue-next.js", "main:umd": "dist/umd/lucide-vue-next.js", "module": "dist/esm/lucide-vue-next.js", "unpkg": "dist/umd/lucide-vue-next.min.js", "typings": "dist/lucide-vue-next.d.ts", "sideEffects": false, "files": ["dist", "nuxt.js"], "devDependencies": {"@testing-library/jest-dom": "^6.1.6", "@testing-library/vue": "^8.0.3", "@vitejs/plugin-vue": "^4.6.2", "@vue/test-utils": "2.4.5", "rollup": "^4.22.4", "rollup-plugin-dts": "^6.1.0", "vite": "^6.3.4", "vitest": "^3.1.3", "vue": "^3.4.21", "@lucide/shared": "1.0.0", "@lucide/rollup-plugins": "1.0.0", "@lucide/build-icons": "1.1.0"}, "peerDependencies": {"vue": ">=3.0.1"}, "scripts": {"build": "pnpm clean && pnpm copy:license && pnpm build:icons && pnpm build:bundles", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && rm -rf ./src/icons/*.ts", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mts --renderUniqueKey --withAliases --aliasesFileExtension=.ts --iconFileExtension=.ts --exportFileName=index.ts", "build:bundles": "rollup -c ./rollup.config.mjs", "test": "pnpm build:icons && vitest run", "test:watch": "vitest watch", "version": "pnpm version --git-tag-version=false"}}