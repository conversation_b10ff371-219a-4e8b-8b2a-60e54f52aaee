import { defineStore } from 'pinia'

export const useAppStore = defineStore('app', {
  state: () => ({
    // 当前激活的顶部标签页
    activeTab: 'explore',
    // 当前在右侧内容区展示的 CVE ID
    selectedCveId: null,
    // 当前视图模式：'search' | 'collection' | 'welcome'
    viewMode: 'welcome',
    // 当前查看的收藏夹名称（当 viewMode 为 'collection' 时使用）
    currentCollectionName: null,
    // 应用加载状态
    isLoading: false,
    // 全局错误信息
    error: null,
    // 主题模式
    isDarkMode: false
  }),

  getters: {
    // 是否在探索页面
    isExploreView: (state) => state.activeTab === 'explore',
    // 是否在设置页面
    isSettingsView: (state) => state.activeTab === 'settings',
    // 是否在帮助页面
    isHelpView: (state) => state.activeTab === 'help',
    // 是否有选中的 CVE
    hasSelectedCve: (state) => !!state.selectedCveId,
    // 是否在查看收藏夹
    isViewingCollection: (state) => state.viewMode === 'collection'
  },

  actions: {
    /**
     * 设置当前激活的顶部标签页
     * @param {string} tabName - 标签页名称 ('explore' | 'settings' | 'help')
     */
    setActiveTab(tabName) {
      this.activeTab = tabName
      // 切换标签页时清除选中的 CVE
      if (tabName !== 'explore') {
        this.selectedCveId = null
        this.viewMode = 'welcome'
      }
    },

    /**
     * 选中一个 CVE，在右侧显示其详情
     * @param {string} cveId - CVE ID
     */
    selectCve(cveId) {
      this.selectedCveId = cveId
      this.viewMode = 'search'
    },

    /**
     * 清除选中的 CVE
     */
    clearSelectedCve() {
      this.selectedCveId = null
      this.viewMode = 'welcome'
    },

    /**
     * 设置视图模式为查看收藏夹
     * @param {string} collectionName - 收藏夹名称
     */
    viewCollection(collectionName) {
      this.viewMode = 'collection'
      this.currentCollectionName = collectionName
      this.selectedCveId = null
    },

    /**
     * 设置视图模式为搜索结果
     */
    viewSearchResults() {
      this.viewMode = 'search'
      this.currentCollectionName = null
    },

    /**
     * 设置加载状态
     * @param {boolean} loading - 是否加载中
     */
    setLoading(loading) {
      this.isLoading = loading
    },

    /**
     * 设置错误信息
     * @param {string|null} error - 错误信息
     */
    setError(error) {
      this.error = error
    },

    /**
     * 清除错误信息
     */
    clearError() {
      this.error = null
    },

    /**
     * 切换主题模式
     */
    toggleTheme() {
      this.isDarkMode = !this.isDarkMode
      // 保存到 localStorage
      localStorage.setItem('cve-platform-theme', this.isDarkMode ? 'dark' : 'light')
      // 应用主题到 document
      this.applyTheme()
    },

    /**
     * 初始化主题
     */
    initTheme() {
      const savedTheme = localStorage.getItem('cve-platform-theme')
      if (savedTheme) {
        this.isDarkMode = savedTheme === 'dark'
      } else {
        // 检测系统主题偏好
        this.isDarkMode = window.matchMedia('(prefers-color-scheme: dark)').matches
      }
      this.applyTheme()
    },

    /**
     * 应用主题到 document
     */
    applyTheme() {
      if (this.isDarkMode) {
        document.documentElement.classList.add('dark')
      } else {
        document.documentElement.classList.remove('dark')
      }
    }
  }
})
