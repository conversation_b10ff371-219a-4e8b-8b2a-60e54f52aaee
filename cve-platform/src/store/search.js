import { defineStore } from 'pinia'
import { searchCves } from '@/api/modules/cve'

export const useSearchStore = defineStore('search', {
  state: () => ({
    // 搜索关键词
    query: '',
    // 筛选条件
    filters: {
      severity: [] // ['critical', 'high', 'medium', 'low']
    },
    // 排序条件
    sorting: {
      by: 'publishedDate', // 'publishedDate' | 'cvssScore'
      order: 'desc' // 'asc' | 'desc'
    },
    // 分页状态
    pagination: {
      currentPage: 1,
      pageSize: 25,
      totalItems: 0,
      totalPages: 1
    },
    // 当前页的搜索结果
    results: [],
    // 是否正在加载搜索结果
    isLoading: false,
    // 搜索错误信息
    error: null,
    // 是否已执行过搜索
    hasSearched: false
  }),

  getters: {
    // 是否有搜索结果
    hasResults: (state) => state.results.length > 0,
    // 是否有筛选条件
    hasFilters: (state) => state.filters.severity.length > 0,
    // 当前页码范围信息
    pageInfo: (state) => {
      const start = (state.pagination.currentPage - 1) * state.pagination.pageSize + 1
      const end = Math.min(start + state.pagination.pageSize - 1, state.pagination.totalItems)
      return {
        start,
        end,
        total: state.pagination.totalItems
      }
    },
    // 是否可以上一页
    canGoPrevious: (state) => state.pagination.currentPage > 1,
    // 是否可以下一页
    canGoNext: (state) => state.pagination.currentPage < state.pagination.totalPages,
    // 搜索参数对象
    searchParams: (state) => ({
      q: state.query,
      severity: state.filters.severity.join(','),
      sortBy: state.sorting.by,
      sortOrder: state.sorting.order,
      page: state.pagination.currentPage,
      pageSize: state.pagination.pageSize
    })
  },

  actions: {
    /**
     * 更新搜索关键词
     * @param {string} q - 搜索关键词
     */
    setQuery(q) {
      this.query = q
    },

    /**
     * 更新筛选条件
     * @param {object} newFilters - 新的筛选条件
     */
    setFilters(newFilters) {
      this.filters = { ...this.filters, ...newFilters }
      // 筛选条件变化时重置到第一页
      this.pagination.currentPage = 1
    },

    /**
     * 更新排序条件
     * @param {object} newSorting - 新的排序条件
     */
    setSorting(newSorting) {
      this.sorting = { ...this.sorting, ...newSorting }
      // 排序条件变化时重置到第一页
      this.pagination.currentPage = 1
    },

    /**
     * 设置当前页码并触发新的搜索
     * @param {number} page - 页码
     */
    setCurrentPage(page) {
      this.pagination.currentPage = page
      this.executeSearch()
    },

    /**
     * 设置每页显示数量
     * @param {number} pageSize - 每页显示数量
     */
    setPageSize(pageSize) {
      this.pagination.pageSize = pageSize
      this.pagination.currentPage = 1
      this.executeSearch()
    },

    /**
     * 执行搜索
     */
    async executeSearch() {
      this.isLoading = true
      this.error = null

      try {
        const response = await searchCves(this.searchParams)
        
        this.results = response.data.items
        this.pagination = {
          ...this.pagination,
          ...response.data.pagination
        }
        this.hasSearched = true
      } catch (error) {
        this.error = error.message || '搜索失败，请稍后重试'
        this.results = []
        this.pagination.totalItems = 0
        this.pagination.totalPages = 1
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 重置搜索状态
     */
    resetSearch() {
      this.query = ''
      this.filters.severity = []
      this.pagination.currentPage = 1
      this.results = []
      this.pagination.totalItems = 0
      this.pagination.totalPages = 1
      this.hasSearched = false
      this.error = null
    },

    /**
     * 清除搜索结果
     */
    clearResults() {
      this.results = []
      this.pagination.totalItems = 0
      this.pagination.totalPages = 1
      this.hasSearched = false
    }
  }
})
