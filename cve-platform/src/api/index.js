import axios from 'axios'
import { mockApi } from './mocks'

// 创建 axios 实例
const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 在开发模式下使用 Mock 数据
    if (import.meta.env.VITE_DEBUG_MODE === 'true') {
      console.log('[API Mock] Request:', config.method?.toUpperCase(), config.url, config.params || config.data)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response) => {
    // 在开发模式下记录响应
    if (import.meta.env.VITE_DEBUG_MODE === 'true') {
      console.log('[API Mock] Response:', response.config.url, response.data)
    }
    return response
  },
  (error) => {
    console.error('[API Error]:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

/**
 * 通用 API 请求函数
 * @param {string} method - HTTP 方法
 * @param {string} url - 请求 URL
 * @param {object} data - 请求数据
 * @param {object} config - 额外配置
 */
async function request(method, url, data = null, config = {}) {
  // 在开发模式下使用 Mock 数据
  if (import.meta.env.VITE_DEBUG_MODE === 'true') {
    return mockApi.request(method, url, data, config)
  }

  try {
    const response = await apiClient({
      method,
      url,
      ...(method.toLowerCase() === 'get' ? { params: data } : { data }),
      ...config
    })
    return response.data
  } catch (error) {
    throw new Error(error.response?.data?.message || error.message || '请求失败')
  }
}

// 导出常用的 HTTP 方法
export const api = {
  get: (url, params, config) => request('GET', url, params, config),
  post: (url, data, config) => request('POST', url, data, config),
  put: (url, data, config) => request('PUT', url, data, config),
  delete: (url, data, config) => request('DELETE', url, data, config),
  patch: (url, data, config) => request('PATCH', url, data, config)
}

export default apiClient
