<template>
  <div class="explore-view">
    <!-- 左侧边栏 -->
    <div class="sidebar">
      <TheSidebar />
    </div>

    <!-- 右侧内容区 -->
    <div class="content-area">
      <!-- 欢迎页面 -->
      <div v-if="appStore.viewMode === 'welcome'" class="welcome-content">
        <div class="welcome-container">
          <div class="welcome-header">
            <div class="logo">
              <n-icon :component="Shield" size="64" color="#18a058" />
            </div>
            <h1 class="welcome-title">漏洞情报分析平台</h1>
            <p class="welcome-subtitle">专业的 CVE 漏洞搜索、分析与管理工具</p>
          </div>

          <div class="welcome-features">
            <div class="feature-grid">
              <div class="feature-item">
                <n-icon :component="Search" size="32" color="#18a058" />
                <h3>智能搜索</h3>
                <p>支持 CVE-ID、产品名、关键词等多维度搜索</p>
              </div>
              <div class="feature-item">
                <n-icon :component="Filter" size="32" color="#18a058" />
                <h3>精准筛选</h3>
                <p>按严重等级、发布时间等条件快速筛选</p>
              </div>
              <div class="feature-item">
                <n-icon :component="Star" size="32" color="#18a058" />
                <h3>收藏管理</h3>
                <p>创建自定义收藏夹，分类管理重要漏洞</p>
              </div>
              <div class="feature-item">
                <n-icon :component="FileText" size="32" color="#18a058" />
                <h3>备注记录</h3>
                <p>支持 Markdown 格式的详细备注和分析</p>
              </div>
              <div class="feature-item">
                <n-icon :component="Bot" size="32" color="#18a058" />
                <h3>AI 分析</h3>
                <p>获取 AI 驱动的漏洞深度分析和建议</p>
              </div>
              <div class="feature-item">
                <n-icon :component="Download" size="32" color="#18a058" />
                <h3>数据导出</h3>
                <p>导出收藏夹和备注，支持数据备份恢复</p>
              </div>
            </div>
          </div>

          <div class="welcome-actions">
            <n-button
              type="primary"
              size="large"
              @click="startSearch"
            >
              <template #icon>
                <n-icon :component="Search" />
              </template>
              开始搜索
            </n-button>
            <n-button
              size="large"
              @click="viewHelp"
            >
              <template #icon>
                <n-icon :component="HelpCircle" />
              </template>
              查看帮助
            </n-button>
          </div>

          <div class="welcome-stats">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-number">{{ statistics.totalCves || '---' }}</div>
                <div class="stat-label">总漏洞数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ statistics.criticalCount || '---' }}</div>
                <div class="stat-label">严重漏洞</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ statistics.highCount || '---' }}</div>
                <div class="stat-label">高危漏洞</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">{{ collectionsStore.collectionNames.length }}</div>
                <div class="stat-label">收藏夹</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索结果列表视图 -->
      <div v-else-if="appStore.viewMode === 'search'" class="search-results-view">
        <SearchResultsView @open-notes="handleOpenNotes" />
      </div>

      <!-- 漏洞详情卡片 -->
      <div v-else-if="appStore.selectedCveId" class="vulnerability-detail">
        <VulnerabilityCard
          :cve="selectedCve"
          @open-notes="handleOpenNotes"
        />
      </div>

      <!-- 收藏夹视图 -->
      <div v-else-if="appStore.viewMode === 'collection'" class="collection-view">
        <CollectionView :collection-name="appStore.currentCollectionName" />
      </div>

      <!-- 加载状态 -->
      <div v-else-if="isLoading" class="loading-content">
        <n-spin size="large" />
        <div class="loading-text">加载中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="appStore.error" class="error-content">
        <n-result
          status="error"
          title="加载失败"
          :description="appStore.error"
        >
          <template #footer>
            <n-button @click="retry">重试</n-button>
          </template>
        </n-result>
      </div>


    </div>

    <!-- 备注模态框 -->
    <NotesModal
      v-model:show="showNotesModal"
      :cve-id="notesCveId"
      @saved="handleNotesSaved"
      @deleted="handleNotesDeleted"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import {
  NIcon,
  NButton,
  NSpin,
  NResult,
  useMessage
} from 'naive-ui'
import {
  Shield,
  Search,
  Filter,
  Star,
  FileText,
  Bot,
  Download,
  HelpCircle
} from 'lucide-vue-next'
import { useAppStore } from '@/store/app'
import { useSearchStore } from '@/store/search'
import { useCollectionsStore } from '@/store/collections'
import { getCveDetails } from '@/api/modules/cve'
import { getStatistics } from '@/api/modules/utils'
import TheSidebar from '@/components/layout/TheSidebar.vue'
import VulnerabilityCard from '@/components/common/VulnerabilityCard.vue'
import NotesModal from '@/components/common/NotesModal.vue'
import CollectionView from '@/components/collections/CollectionView.vue'
import SearchResultsView from '@/components/search/SearchResultsView.vue'

const appStore = useAppStore()
const searchStore = useSearchStore()
const collectionsStore = useCollectionsStore()
const message = useMessage()

// 响应式数据
const selectedCve = ref(null)
const isLoading = ref(false)
const statistics = ref({})
const showNotesModal = ref(false)
const notesCveId = ref('')

// 计算属性

// 生命周期
onMounted(async () => {
  collectionsStore.init()
  await loadStatistics()
  
  // 如果有选中的 CVE，加载其详情
  if (appStore.selectedCveId) {
    await loadCveDetails(appStore.selectedCveId)
  }
})

// 监听选中的 CVE 变化
watch(() => appStore.selectedCveId, async (newCveId) => {
  if (newCveId) {
    await loadCveDetails(newCveId)
  } else {
    selectedCve.value = null
  }
})

// 方法
const loadStatistics = async () => {
  try {
    const response = await getStatistics()
    statistics.value = response.data
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

const loadCveDetails = async (cveId) => {
  isLoading.value = true
  try {
    const response = await getCveDetails(cveId)
    selectedCve.value = response.data
  } catch (error) {
    message.error('加载漏洞详情失败：' + error.message)
    appStore.setError(error.message)
  } finally {
    isLoading.value = false
  }
}

const startSearch = () => {
  // 聚焦到搜索框
  const searchInput = document.querySelector('.search-input input')
  if (searchInput) {
    searchInput.focus()
  }
}

const viewHelp = () => {
  appStore.setActiveTab('help')
}

const retry = () => {
  appStore.clearError()
  if (appStore.selectedCveId) {
    loadCveDetails(appStore.selectedCveId)
  }
}

const handleOpenNotes = (cveId) => {
  notesCveId.value = cveId
  showNotesModal.value = true
}

const handleNotesSaved = (cveId, content) => {
  message.success('备注保存成功')
}

const handleNotesDeleted = (cveId) => {
  message.success('备注删除成功')
}
</script>

<style scoped>
.explore-view {
  display: flex;
  height: 100vh;
  overflow: hidden;
}

.sidebar {
  width: 400px;
  flex-shrink: 0;
  border-right: 1px solid #e0e0e6;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.welcome-content {
  flex: 1;
  overflow-y: auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.welcome-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  gap: 48px;
}

.welcome-header {
  text-align: center;
}

.logo {
  margin-bottom: 24px;
}

.welcome-title {
  font-size: 36px;
  font-weight: 700;
  color: #333;
  margin: 0 0 12px 0;
}

.welcome-subtitle {
  font-size: 18px;
  color: #666;
  margin: 0;
}

.welcome-features {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 32px;
}

.feature-item {
  text-align: center;
  padding: 24px;
  border-radius: 8px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.feature-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(24, 160, 88, 0.15);
}

.feature-item h3 {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 16px 0 8px 0;
}

.feature-item p {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin: 0;
}

.welcome-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.welcome-stats {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 16px;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  color: #18a058;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.search-results-view {
  flex: 1;
  overflow: hidden;
  background: #f8f9fa;
}

.vulnerability-detail {
  flex: 1;
  overflow: hidden;
  padding: 24px;
  background: #f8f9fa;
}

.collection-view {
  flex: 1;
  overflow: hidden;
  padding: 24px;
  background: #f8f9fa;
}

.loading-content,
.error-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.pagination-section {
  flex-shrink: 0;
  padding: 16px 24px;
  background: white;
  border-top: 1px solid #e0e0e6;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .sidebar {
    width: 350px;
  }

  .welcome-container {
    padding: 40px 24px;
  }

  .feature-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 24px;
  }
}

@media (max-width: 768px) {
  .explore-view {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 300px;
    border-right: none;
    border-bottom: 1px solid #e0e0e6;
  }

  .welcome-container {
    padding: 24px 16px;
    gap: 32px;
  }

  .welcome-title {
    font-size: 28px;
  }

  .welcome-subtitle {
    font-size: 16px;
  }

  .feature-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .welcome-features,
  .welcome-stats {
    padding: 24px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .welcome-actions {
    flex-direction: column;
    align-items: center;
  }

  .vulnerability-detail,
  .collection-view {
    padding: 16px;
  }
}
</style>
