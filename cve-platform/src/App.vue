<template>
  <n-config-provider :theme="theme" :locale="zhCN" :date-locale="dateZhCN">
    <n-message-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <div class="app">
            <!-- 顶部标签页 -->
            <div class="app-header">
              <TheHeader />
            </div>

            <!-- 主要内容区域 -->
            <div class="app-main">
              <router-view />
            </div>

            <!-- 底部 -->
            <div class="app-footer">
              <TheFooter />
            </div>
          </div>
        </n-notification-provider>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup>
import { computed, onMounted } from 'vue'
import {
  NConfigProvider,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  zhCN,
  dateZhCN,
  darkTheme
} from 'naive-ui'
import { useAppStore } from '@/store/app'
import TheHeader from '@/components/layout/TheHeader.vue'
import TheFooter from '@/components/layout/TheFooter.vue'

const appStore = useAppStore()

// 主题配置
const theme = computed(() => {
  return appStore.isDarkMode ? darkTheme : null
})

// 初始化主题
onMounted(() => {
  appStore.initTheme()
})
</script>

<style>
/* 全局样式重置 */
* {
  box-sizing: border-box;
}

:root {
  --bg-color: #f5f5f5;
  --bg-color-secondary: #ffffff;
  --text-color: #333333;
  --text-color-secondary: #666666;
  --border-color: #e0e0e6;
}

:root.dark {
  --bg-color: #1a1a1a;
  --bg-color-secondary: #2d2d2d;
  --text-color: #ffffff;
  --text-color-secondary: #cccccc;
  --border-color: #404040;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

#app {
  height: 100vh;
  overflow: hidden;
}

.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.app-header {
  flex-shrink: 0;
  z-index: 100;
}

.app-main {
  flex: 1;
  overflow: hidden;
}

.app-footer {
  flex-shrink: 0;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 代码字体 */
code, pre, .code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'source-code-pro', monospace;
}

/* 链接样式 */
a {
  color: #18a058;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* 选择文本样式 */
::selection {
  background-color: rgba(24, 160, 88, 0.2);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid #18a058;
  outline-offset: 2px;
}

/* 禁用状态样式 */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 工具类 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

.p-2 {
  padding: 8px;
}

.p-4 {
  padding: 16px;
}

.p-6 {
  padding: 24px;
}

.m-2 {
  margin: 8px;
}

.m-4 {
  margin: 16px;
}

.m-6 {
  margin: 24px;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .md\:hidden {
    display: none;
  }

  .md\:flex {
    display: flex;
  }
}

@media (max-width: 480px) {
  .sm\:hidden {
    display: none;
  }

  .sm\:flex {
    display: flex;
  }
}
</style>
