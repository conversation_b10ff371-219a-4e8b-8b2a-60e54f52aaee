<template>
  <div class="header-container">
    <n-tabs
      v-model:value="activeTab"
      type="line"
      size="large"
      class="header-tabs"
      @update:value="handleTabChange"
    >
      <n-tab-pane name="explore" tab="探索">
        <!-- 探索页面内容由路由处理 -->
      </n-tab-pane>
      <n-tab-pane name="settings" tab="设置">
        <!-- 设置页面内容由路由处理 -->
      </n-tab-pane>
      <n-tab-pane name="help" tab="帮助">
        <!-- 帮助页面内容由路由处理 -->
      </n-tab-pane>
    </n-tabs>
    
    <!-- 应用标题和控制 -->
    <div class="header-info">
      <div class="app-title">
        {{ appTitle }}
      </div>
      <div class="header-controls">
        <div v-if="isLoading" class="loading-indicator">
          <n-spin size="small" />
        </div>
        <!-- 主题切换按钮 -->
        <n-button
          size="medium"
          quaternary
          circle
          @click="toggleTheme"
        >
          <template #icon>
            <n-icon :component="isDarkMode ? Sun : Moon" />
          </template>
        </n-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { NTabs, NTabPane, NSpin, NButton, NIcon } from 'naive-ui'
import { useRouter } from 'vue-router'
import { Sun, Moon } from 'lucide-vue-next'
import { useAppStore } from '@/store/app'

const appStore = useAppStore()
const router = useRouter()

// 计算属性
const activeTab = computed({
  get: () => appStore.activeTab,
  set: (value) => appStore.setActiveTab(value)
})

const isLoading = computed(() => appStore.isLoading)

const appTitle = computed(() => {
  return import.meta.env.VITE_APP_TITLE || '漏洞情报分析平台'
})

const isDarkMode = computed(() => appStore.isDarkMode)

// 方法
const handleTabChange = (tabName) => {
  appStore.setActiveTab(tabName)
  // 导航到对应路由
  router.push(`/${tabName}`)
}

const toggleTheme = () => {
  appStore.toggleTheme()
}
</script>

<style scoped>
.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 24px;
  background: white;
  border-bottom: 1px solid #e0e0e6;
  height: 64px;
}

.header-tabs {
  flex: 1;
}

.header-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.app-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-indicator {
  display: flex;
  align-items: center;
}

/* 自定义标签页样式 */
:deep(.n-tabs-nav) {
  border-bottom: none;
}

:deep(.n-tabs-tab) {
  font-size: 16px;
  font-weight: 500;
  padding: 0 24px;
}

:deep(.n-tabs-tab:hover) {
  color: #18a058;
}

:deep(.n-tabs-tab--active) {
  color: #18a058;
  font-weight: 600;
}

:deep(.n-tabs-bar) {
  background-color: #18a058;
}
</style>
