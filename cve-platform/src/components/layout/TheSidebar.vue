<template>
  <div class="sidebar-container">
    <!-- 搜索与控制区域 -->
    <div class="search-section">
      <SearchControl />
    </div>

    <!-- 收藏夹或搜索结果区域 -->
    <div class="content-section">
      <!-- 搜索结果列表 -->
      <div v-if="showSearchResults" class="search-results">
        <div class="section-title">
          <span>搜索结果</span>
          <n-badge 
            v-if="searchStore.hasResults" 
            :value="searchStore.pagination.totalItems" 
            :max="999"
            type="info"
          />
        </div>
        <ResultList />
      </div>

      <!-- 收藏夹列表 -->
      <div v-else class="collections-section">
        <CollectionList />
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { NBadge } from 'naive-ui'
import { useAppStore } from '@/store/app'
import { useSearchStore } from '@/store/search'
import SearchControl from '@/components/search/SearchControl.vue'
import ResultList from '@/components/search/ResultList.vue'
import CollectionList from '@/components/collections/CollectionList.vue'

const appStore = useAppStore()
const searchStore = useSearchStore()

// 计算属性
const showSearchResults = computed(() => {
  return appStore.viewMode === 'search' && searchStore.hasSearched
})
</script>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #e0e0e6;
}

.search-section {
  flex-shrink: 0;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e0e0e6;
}

.content-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.search-results {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.collections-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #666;
  background: white;
  border-bottom: 1px solid #e0e0e6;
}
</style>
