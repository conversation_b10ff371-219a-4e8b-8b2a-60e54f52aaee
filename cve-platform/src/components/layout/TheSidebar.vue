<template>
  <div class="sidebar-container">
    <!-- 搜索与控制区域 -->
    <div class="search-section">
      <SearchControl />
    </div>

    <!-- 收藏夹列表 -->
    <div class="content-section">
      <CollectionList />
    </div>
  </div>
</template>

<script setup>
import SearchControl from '@/components/search/SearchControl.vue'
import CollectionList from '@/components/collections/CollectionList.vue'
</script>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #fafafa;
  border-right: 1px solid #e0e0e6;
}

.search-section {
  flex-shrink: 0;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e0e0e6;
}

.content-section {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
</style>
