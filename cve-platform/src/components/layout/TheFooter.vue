<template>
  <footer class="footer-container">
    <div class="footer-content">
      <div class="copyright">
        © {{ currentYear }} 漏洞情报分析平台
      </div>
      <div class="footer-links">
        <span class="version">v{{ version }}</span>
        <span v-if="isDevelopment" class="dev-mode">开发模式</span>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'

// 计算属性
const currentYear = computed(() => new Date().getFullYear())

const version = computed(() => '1.0.0')

const isDevelopment = computed(() => {
  return import.meta.env.VITE_DEBUG_MODE === 'true'
})
</script>

<style scoped>
.footer-container {
  flex-shrink: 0;
  background: #f5f5f5;
  border-top: 1px solid #e0e0e6;
  padding: 12px 24px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.copyright {
  font-size: 12px;
  color: #666;
}

.footer-links {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.version {
  color: #666;
}

.dev-mode {
  background: #ff6b35;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
  font-weight: 500;
}
</style>
