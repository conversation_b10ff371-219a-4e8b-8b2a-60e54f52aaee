<template>
  <div class="search-control">
    <!-- 搜索框 -->
    <div class="search-input-section">
      <n-input
        v-model:value="searchQuery"
        placeholder="输入 CVE-ID, 产品名, 关键词..."
        size="large"
        clearable
        @keyup.enter="handleSearch"
      >
        <template #prefix>
          <n-icon :component="Search" />
        </template>
      </n-input>
      <n-button
        type="primary"
        size="large"
        :loading="searchStore.isLoading"
        @click="handleSearch"
      >
        搜索
      </n-button>
    </div>

    <!-- 筛选工具栏 -->
    <div class="filter-section">
      <!-- 严重等级筛选 -->
      <div class="filter-group">
        <div class="filter-label">严重等级</div>
        <n-checkbox-group v-model:value="selectedSeverities" @update:value="handleSeverityChange">
          <n-space>
            <n-checkbox value="CRITICAL">
              <n-tag type="error" size="small">🟪 严重</n-tag>
            </n-checkbox>
            <n-checkbox value="HIGH">
              <n-tag type="warning" size="small">🟥 高危</n-tag>
            </n-checkbox>
            <n-checkbox value="MEDIUM">
              <n-tag type="info" size="small">🟧 中危</n-tag>
            </n-checkbox>
            <n-checkbox value="LOW">
              <n-tag type="success" size="small">🟨 低危</n-tag>
            </n-checkbox>
          </n-space>
        </n-checkbox-group>
      </div>

      <!-- 排序控制 -->
      <div class="sort-section">
        <div class="sort-group">
          <div class="sort-label">排序</div>
          <n-select
            v-model:value="sortBy"
            :options="sortOptions"
            size="small"
            style="width: 120px"
            @update:value="handleSortChange"
          />
          <n-button
            size="small"
            :type="sortOrder === 'desc' ? 'primary' : 'default'"
            @click="toggleSortOrder"
          >
            <n-icon :component="sortOrder === 'desc' ? ArrowDown : ArrowUp" />
          </n-button>
        </div>

        <!-- 每页显示数量 -->
        <div class="pagesize-group">
          <div class="sort-label">每页</div>
          <n-select
            v-model:value="pageSize"
            :options="pageSizeOptions"
            size="small"
            style="width: 80px"
            @update:value="handlePageSizeChange"
          />
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import {
  NInput,
  NButton,
  NCheckboxGroup,
  NCheckbox,
  NTag,
  NSelect,
  NIcon,
  NSpin,
  NSpace
} from 'naive-ui'
import {
  Search,
  ArrowDown,
  ArrowUp
} from 'lucide-vue-next'
import { useSearchStore } from '@/store/search'
import { useAppStore } from '@/store/app'

const searchStore = useSearchStore()
const appStore = useAppStore()

// 响应式数据
const searchQuery = ref('')
const selectedSeverities = ref([])
const sortBy = ref('publishedDate')
const sortOrder = ref('desc')
const pageSize = ref(25)

// 选项数据
const sortOptions = [
  { label: '发布时间', value: 'publishedDate' },
  { label: '严重程度', value: 'cvssScore' }
]

const pageSizeOptions = [
  { label: '10', value: 10 },
  { label: '25', value: 25 },
  { label: '50', value: 50 }
]

// 计算属性
const hasFilters = computed(() => {
  return selectedSeverities.value.length > 0
})

// 监听器
watch(() => searchStore.query, (newQuery) => {
  searchQuery.value = newQuery
})

watch(() => searchStore.filters.severity, (newSeverities) => {
  selectedSeverities.value = [...newSeverities]
})

watch(() => searchStore.sorting, (newSorting) => {
  sortBy.value = newSorting.by
  sortOrder.value = newSorting.order
})

watch(() => searchStore.pagination.pageSize, (newPageSize) => {
  pageSize.value = newPageSize
})

// 方法
const handleSearch = () => {
  searchStore.setQuery(searchQuery.value)
  searchStore.executeSearch()
  appStore.viewSearchResults()
}

const handleSeverityChange = (severities) => {
  searchStore.setFilters({ severity: severities })
  if (searchStore.hasSearched) {
    searchStore.executeSearch()
  }
}

const handleSortChange = (newSortBy) => {
  searchStore.setSorting({ by: newSortBy })
  if (searchStore.hasSearched) {
    searchStore.executeSearch()
  }
}

const toggleSortOrder = () => {
  const newOrder = sortOrder.value === 'desc' ? 'asc' : 'desc'
  sortOrder.value = newOrder
  searchStore.setSorting({ order: newOrder })
  if (searchStore.hasSearched) {
    searchStore.executeSearch()
  }
}

const handlePageSizeChange = (newPageSize) => {
  searchStore.setPageSize(newPageSize)
}
</script>

<style scoped>
.search-control {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.search-input-section {
  display: flex;
  gap: 8px;
}

.filter-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}

.sort-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.sort-group,
.pagesize-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sort-label {
  font-size: 12px;
  font-weight: 600;
  color: #666;
}



/* 自定义标签样式 */
:deep(.n-tag) {
  cursor: pointer;
}

/* 自定义复选框样式 */
:deep(.n-checkbox) {
  align-items: center;
}
</style>
