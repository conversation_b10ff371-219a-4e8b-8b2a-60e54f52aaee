<template>
  <div class="search-results-view">
    <!-- 搜索结果头部 -->
    <div class="results-header">
      <div class="results-info">
        <h2 class="results-title">搜索结果</h2>
        <div v-if="searchStore.hasResults" class="results-meta">
          <span class="results-count">
            找到 {{ searchStore.pagination.totalItems }} 个结果
          </span>
          <span class="results-range">
            (第 {{ searchStore.pageInfo.start }}-{{ searchStore.pageInfo.end }} 项)
          </span>
        </div>
      </div>
      
      <div class="results-actions">
        <div class="sort-controls">
          <span class="sort-label">排序：</span>
          <n-select
            v-model:value="sortBy"
            :options="sortOptions"
            size="small"
            style="width: 120px"
            @update:value="handleSortChange"
          />
          <n-button
            size="small"
            :type="sortOrder === 'desc' ? 'primary' : 'default'"
            @click="toggleSortOrder"
          >
            <n-icon :component="sortOrder === 'desc' ? ArrowDown : ArrowUp" />
          </n-button>
        </div>
      </div>
    </div>

    <!-- 搜索结果内容 -->
    <div class="results-content">
      <!-- 加载状态 -->
      <div v-if="searchStore.isLoading" class="loading-container">
        <n-spin size="large" />
        <div class="loading-text">搜索中...</div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="searchStore.error" class="error-container">
        <n-result status="error" title="搜索失败" :description="searchStore.error">
          <template #footer>
            <n-button @click="retrySearch">重试</n-button>
          </template>
        </n-result>
      </div>

      <!-- 空结果状态 -->
      <div v-else-if="!searchStore.hasResults && searchStore.hasSearched" class="empty-container">
        <n-result status="404" title="未找到结果" description="请尝试调整搜索条件">
          <template #footer>
            <n-button @click="clearSearch">清除搜索</n-button>
          </template>
        </n-result>
      </div>

      <!-- 结果网格 -->
      <div v-else-if="searchStore.hasResults" class="results-grid">
        <div
          v-for="cve in searchStore.results"
          :key="cve.id"
          class="cve-card"
          :class="{ 'selected': isSelected(cve.id) }"
          @click="selectCve(cve.id)"
        >
          <div class="cve-card-header">
            <div class="cve-id">{{ cve.id }}</div>
            <div class="cve-actions">
              <n-button
                size="tiny"
                quaternary
                type="info"
                @click.stop="getAiSummary(cve.id)"
              >
                <template #icon>
                  <n-icon :component="Bot" />
                </template>
              </n-button>
              <n-button
                size="tiny"
                quaternary
                @click.stop="openNotes(cve.id)"
              >
                <template #icon>
                  <n-icon
                    :component="FileText"
                    :color="hasNote(cve.id) ? '#18a058' : '#ccc'"
                  />
                </template>
              </n-button>
              <n-button
                size="tiny"
                quaternary
                type="primary"
                @click.stop="addToCollections(cve.id)"
              >
                <template #icon>
                  <n-icon :component="Star" />
                </template>
              </n-button>
            </div>
          </div>
          
          <div class="cve-severity">
            <n-tag
              :type="getSeverityType(cve.cvssV3.baseSeverity)"
              size="small"
            >
              {{ getSeverityLabel(cve.cvssV3.baseSeverity) }}
            </n-tag>
            <span class="cvss-score">{{ cve.cvssV3.baseScore }}</span>
          </div>
          
          <div class="cve-title">{{ cve.title }}</div>
          
          <div class="cve-meta">
            <span class="publish-date">
              {{ formatDate(cve.publishedDate) }}
            </span>
            <div class="cve-indicators">
              <n-icon
                v-if="hasNote(cve.id)"
                :component="FileText"
                size="14"
                color="#18a058"
              />
              <n-icon
                v-if="isInAnyCollection(cve.id)"
                :component="Star"
                size="14"
                color="#f0a020"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页控件 -->
    <div v-if="showPagination" class="pagination-section">
      <n-pagination
        v-model:page="currentPage"
        :page-count="searchStore.pagination.totalPages"
        :page-size="searchStore.pagination.pageSize"
        :item-count="searchStore.pagination.totalItems"
        show-size-picker
        show-quick-jumper
        :page-sizes="[10, 25, 50]"
        @update:page="handlePageChange"
        @update:page-size="handlePageSizeChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import {
  NSelect,
  NButton,
  NIcon,
  NTag,
  NSpin,
  NResult,
  NPagination,
  useMessage
} from 'naive-ui'
import {
  ArrowDown,
  ArrowUp,
  FileText,
  Star,
  Bot
} from 'lucide-vue-next'
import { useSearchStore } from '@/store/search'
import { useAppStore } from '@/store/app'
import { useCollectionsStore } from '@/store/collections'

const emit = defineEmits(['openNotes', 'getAiSummary'])

const searchStore = useSearchStore()
const appStore = useAppStore()
const collectionsStore = useCollectionsStore()
const message = useMessage()

// 响应式数据
const sortBy = ref('publishedDate')
const sortOrder = ref('desc')

// 排序选项
const sortOptions = [
  { label: '发布时间', value: 'publishedDate' },
  { label: 'CVSS 评分', value: 'cvssScore' }
]

// 计算属性
const currentPage = computed({
  get: () => searchStore.pagination.currentPage,
  set: (value) => searchStore.setCurrentPage(value)
})

const showPagination = computed(() => {
  return searchStore.hasResults && searchStore.pagination.totalPages > 1
})

// 方法
const isSelected = (cveId) => {
  return appStore.selectedCveId === cveId
}

const selectCve = (cveId) => {
  appStore.selectCve(cveId)
}

const getSeverityType = (severity) => {
  const typeMap = {
    'CRITICAL': 'error',
    'HIGH': 'warning',
    'MEDIUM': 'info',
    'LOW': 'success'
  }
  return typeMap[severity] || 'default'
}

const getSeverityLabel = (severity) => {
  const labelMap = {
    'CRITICAL': '严重',
    'HIGH': '高危',
    'MEDIUM': '中危',
    'LOW': '低危'
  }
  return labelMap[severity] || severity
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const hasNote = (cveId) => {
  return collectionsStore.hasCveNote(cveId)
}

const isInAnyCollection = (cveId) => {
  return collectionsStore.getCveCollections(cveId).length > 0
}

const handleSortChange = () => {
  searchStore.setSorting({ by: sortBy.value })
  if (searchStore.hasSearched) {
    searchStore.executeSearch()
  }
}

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'desc' ? 'asc' : 'desc'
  searchStore.setSorting({ order: sortOrder.value })
  if (searchStore.hasSearched) {
    searchStore.executeSearch()
  }
}

const getAiSummary = (cveId) => {
  // 选中CVE并切换到详情视图，然后触发AI分析
  appStore.selectCve(cveId)
  // 通过事件通知父组件触发AI分析
  emit('getAiSummary', cveId)
}

const openNotes = (cveId) => {
  emit('openNotes', cveId)
}

const addToCollections = (cveId) => {
  try {
    collectionsStore.addCveToCollections(cveId)
    const selectedCount = collectionsStore.selectedCollectionsCount
    if (selectedCount === 0) {
      message.success(`已添加到默认收藏夹`)
    } else {
      message.success(`已添加到 ${selectedCount} 个收藏夹`)
    }
  } catch (error) {
    message.error('添加到收藏夹失败')
  }
}

const retrySearch = () => {
  searchStore.executeSearch()
}

const clearSearch = () => {
  searchStore.resetSearch()
  appStore.clearSelectedCve()
}

const handlePageChange = (page) => {
  searchStore.setCurrentPage(page)
}

const handlePageSizeChange = (pageSize) => {
  searchStore.setPageSize(pageSize)
}
</script>

<style scoped>
.search-results-view {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%);
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 32px 24px 24px;
  border-bottom: 1px solid rgba(224, 224, 230, 0.6);
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  backdrop-filter: blur(10px);
}

.results-info {
  flex: 1;
}

.results-title {
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 12px 0;
  letter-spacing: -0.5px;
}

.results-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 15px;
  color: #718096;
}

.results-count {
  font-weight: 600;
  color: #18a058;
}

.results-range {
  color: #a0aec0;
  font-weight: 500;
}

.results-actions {
  display: flex;
  gap: 16px;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.results-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading-container,
.error-container,
.empty-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px;
}

.loading-text {
  font-size: 16px;
  color: #666;
}

.results-grid {
  flex: 1;
  overflow-y: auto;
  padding: 32px 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
  align-content: start;
}

.cve-card {
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  gap: 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  position: relative;
  overflow: hidden;
}

.cve-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #18a058, #36ad6a);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.cve-card:hover {
  border-color: #18a058;
  box-shadow: 0 10px 25px rgba(24, 160, 88, 0.15), 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px);
}

.cve-card:hover::before {
  transform: scaleX(1);
}

.cve-card.selected {
  border-color: #18a058;
  background: linear-gradient(135deg, #f0f9f0 0%, #e8f5e8 100%);
  box-shadow: 0 8px 25px rgba(24, 160, 88, 0.2), 0 4px 6px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.cve-card.selected::before {
  transform: scaleX(1);
}

.cve-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.cve-id {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 18px;
  font-weight: 800;
  color: #1a1a1a;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: 0.5px;
}

.cve-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.cve-card:hover .cve-actions {
  opacity: 1;
}

.cve-severity {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  border: 1px solid rgba(224, 224, 230, 0.5);
}

.cvss-score {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 18px;
  font-weight: 800;
  color: #333;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.cve-title {
  font-size: 15px;
  color: #4a5568;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  flex: 1;
  font-weight: 500;
  margin: 8px 0;
}

.cve-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #718096;
  padding-top: 12px;
  border-top: 1px solid rgba(224, 224, 230, 0.3);
}

.publish-date {
  font-size: 13px;
  font-weight: 500;
  color: #718096;
}

.cve-indicators {
  display: flex;
  gap: 8px;
}

.pagination-section {
  flex-shrink: 0;
  padding: 24px;
  border-top: 1px solid #e0e0e6;
  background: white;
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .results-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 16px;
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .results-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .results-actions {
    justify-content: flex-end;
  }

  .results-grid {
    grid-template-columns: 1fr;
    padding: 16px;
    gap: 12px;
  }

  .cve-card {
    padding: 16px;
  }

  .pagination-section {
    padding: 16px;
  }
}

@media (max-width: 480px) {
  .results-title {
    font-size: 20px;
  }

  .results-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .sort-controls {
    flex-wrap: wrap;
  }
}

/* 自定义分页组件样式 */
:deep(.n-pagination) {
  justify-content: center;
}

:deep(.n-pagination .n-pagination-item--active) {
  background-color: #18a058;
  border-color: #18a058;
}

:deep(.n-pagination .n-pagination-item:hover) {
  border-color: #18a058;
  color: #18a058;
}
</style>
