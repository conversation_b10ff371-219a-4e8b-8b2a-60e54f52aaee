#!/bin/bash

# 漏洞情报分析平台启动脚本
# 用于快速启动开发或生产环境

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}"
    echo "=================================================="
    echo "    漏洞情报分析平台 (CVE Platform)"
    echo "    版本: v1.0.1"
    echo "    技术栈: Vue 3 + Vite + Naive UI"
    echo "=================================================="
    echo -e "${NC}"
}

# 检查Node.js环境
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js 未安装，请先安装 Node.js 16+ 版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js 版本过低，需要 16+ 版本，当前版本: $(node -v)"
        exit 1
    fi
    
    print_message "Node.js 版本检查通过: $(node -v)"
}

# 安装依赖
install_deps() {
    if [ ! -d "node_modules" ]; then
        print_message "安装项目依赖..."
        npm install
    else
        print_message "依赖已安装，跳过安装步骤"
    fi
}

# 开发模式启动
start_dev() {
    print_message "启动开发服务器..."
    print_message "访问地址: http://localhost:5173"
    print_message "按 Ctrl+C 停止服务器"
    echo ""
    npm run dev
}

# 生产模式构建
build_prod() {
    print_message "构建生产版本..."
    npm run build
    print_message "构建完成，产物位于 dist/ 目录"
}

# 预览生产版本
preview_prod() {
    if [ ! -d "dist" ]; then
        print_warning "未找到构建产物，先进行构建..."
        build_prod
    fi
    
    print_message "启动生产版本预览..."
    print_message "访问地址: http://localhost:4173"
    npm run preview
}

# Docker 启动
start_docker() {
    print_message "使用 Docker 启动应用..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 构建 Docker 镜像
    print_message "构建 Docker 镜像..."
    docker build -t cve-platform:latest .
    
    # 启动容器
    print_message "启动 Docker 容器..."
    docker run -d -p 8080:80 --name cve-platform cve-platform:latest
    
    print_message "应用已启动，访问地址: http://localhost:8080"
    print_message "停止容器: docker stop cve-platform"
    print_message "删除容器: docker rm cve-platform"
}

# 运行测试
run_tests() {
    print_message "运行测试用例..."
    npm run test:run
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev      启动开发服务器 (默认)"
    echo "  build    构建生产版本"
    echo "  preview  预览生产版本"
    echo "  docker   使用 Docker 启动"
    echo "  test     运行测试用例"
    echo "  help     显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev     # 启动开发服务器"
    echo "  $0 build   # 构建生产版本"
    echo "  $0 docker  # 使用 Docker 启动"
}

# 主函数
main() {
    print_header
    
    # 检查是否在项目根目录
    if [ ! -f "package.json" ]; then
        print_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 检查环境
    check_node
    install_deps
    
    # 根据参数执行相应操作
    case "${1:-dev}" in
        "dev")
            start_dev
            ;;
        "build")
            build_prod
            ;;
        "preview")
            preview_prod
            ;;
        "docker")
            start_docker
            ;;
        "test")
            run_tests
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
