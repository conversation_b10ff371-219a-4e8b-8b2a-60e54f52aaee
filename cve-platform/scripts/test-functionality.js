#!/usr/bin/env node

/**
 * 功能测试脚本
 * 验证漏洞情报分析平台的所有核心功能
 */

const puppeteer = require('puppeteer');

async function testApplication() {
  console.log('🚀 开始功能测试...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  
  try {
    // 1. 测试页面加载
    console.log('📄 测试页面加载...');
    await page.goto('http://localhost:5173');
    await page.waitForSelector('.welcome-title', { timeout: 5000 });
    console.log('✅ 页面加载成功\n');
    
    // 2. 测试主题切换
    console.log('🎨 测试主题切换...');
    const themeButton = await page.$('button[aria-label*="theme"], button:has(svg)');
    if (themeButton) {
      await themeButton.click();
      await page.waitForTimeout(1000);
      console.log('✅ 主题切换功能正常\n');
    }
    
    // 3. 测试搜索功能
    console.log('🔍 测试搜索功能...');
    await page.type('input[placeholder*="CVE-ID"]', 'log4j');
    await page.click('button:has-text("搜索")');
    await page.waitForTimeout(2000);
    
    // 检查搜索结果
    const searchResults = await page.$('.results-grid');
    if (searchResults) {
      console.log('✅ 搜索功能正常');
      
      // 测试CVE卡片点击
      const cveCard = await page.$('.cve-card');
      if (cveCard) {
        await cveCard.click();
        await page.waitForTimeout(1000);
        console.log('✅ CVE详情显示正常');
      }
    }
    console.log('');
    
    // 4. 测试设置页面
    console.log('⚙️ 测试设置页面...');
    await page.click('text=设置');
    await page.waitForSelector('.settings-title', { timeout: 3000 });
    console.log('✅ 设置页面加载正常\n');
    
    // 5. 测试帮助页面
    console.log('❓ 测试帮助页面...');
    await page.click('text=帮助');
    await page.waitForSelector('.help-title', { timeout: 3000 });
    console.log('✅ 帮助页面加载正常\n');
    
    // 6. 返回探索页面
    console.log('🏠 返回探索页面...');
    await page.click('text=探索');
    await page.waitForSelector('.welcome-title', { timeout: 3000 });
    console.log('✅ 页面导航正常\n');
    
    console.log('🎉 所有功能测试通过！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await browser.close();
  }
}

// 检查服务器是否运行
async function checkServer() {
  try {
    const response = await fetch('http://localhost:5173');
    return response.ok;
  } catch {
    return false;
  }
}

async function main() {
  const serverRunning = await checkServer();
  if (!serverRunning) {
    console.log('❌ 开发服务器未运行，请先启动: npm run dev');
    process.exit(1);
  }
  
  await testApplication();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testApplication };
